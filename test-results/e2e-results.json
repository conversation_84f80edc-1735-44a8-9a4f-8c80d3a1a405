{"config": {"configFile": "/home/<USER>/git/workwell/playwright.config.ts", "rootDir": "/home/<USER>/git/workwell/src/__tests__/e2e", "forbidOnly": false, "fullyParallel": false, "globalSetup": "/home/<USER>/git/workwell/src/__tests__/e2e/global-setup.ts", "globalTeardown": "/home/<USER>/git/workwell/src/__tests__/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/e2e-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/home/<USER>/git/workwell/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/home/<USER>/git/workwell/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 120000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 1, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "admin-api.spec.ts", "file": "admin-api.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Admin API E2E Tests", "file": "admin-api.spec.ts", "line": 4, "column": 6, "specs": [{"title": "Admin API Access and Functionality", "ok": true, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 22592, "errors": [], "stdout": [{"text": "After sign in, current URL: http://localhost:3000/dashboard\n"}, {"text": "✅ Admin authentication verified\n"}, {"text": "✅ Admin companies API working: \u001b[33m20\u001b[39m companies\n"}, {"text": "✅ Test companies found in API response\n"}, {"text": "✅ Admin users API working: \u001b[33m12\u001b[39m users\n"}, {"text": "✅ Admin benefits API working: \u001b[33m50\u001b[39m benefits\n"}, {"text": "✅ All core admin APIs verified: companies, users, benefits\n"}, {"text": "🎉 All admin API functionality verified successfully!\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T19:41:18.699Z", "annotations": [], "attachments": [{"name": "trace", "contentType": "application/zip", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-api-Admin-API-E2E-Te-226f7-PI-Access-and-Functionality-chromium/trace.zip"}]}], "status": "expected"}], "id": "e3d82efe7a3d9e974f14-f7fd8971136538858b9f", "file": "admin-api.spec.ts", "line": 9, "column": 3}, {"title": "Admin Page Loads Successfully", "ok": true, "tags": [], "tests": [{"timeout": 120000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 20742, "errors": [], "stdout": [{"text": "✅ Admin page loads successfully\n"}, {"text": "✅ Admin page URL is stable\n"}, {"text": "🎉 Admin page functionality verified successfully!\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-25T19:41:42.231Z", "annotations": [], "attachments": [{"name": "trace", "contentType": "application/zip", "path": "/home/<USER>/git/workwell/test-results/e2e-artifacts/admin-api-Admin-API-E2E-Tests-Admin-Page-Loads-Successfully-chromium/trace.zip"}]}], "status": "expected"}], "id": "e3d82efe7a3d9e974f14-3eac74905f6ad1515d7f", "file": "admin-api.spec.ts", "line": 99, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-25T19:41:16.761Z", "duration": 47070.591, "expected": 2, "skipped": 0, "unexpected": 0, "flaky": 0}}