{"numTotalTestSuites": 131, "numPassedTestSuites": 131, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 236, "numPassedTests": 235, "numFailedTests": 0, "numPendingTests": 1, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1756155729954, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should reset all analytics data", "status": "passed", "title": "should reset all analytics data", "duration": 4.1820909999999, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should reset specific analytics data type", "status": "passed", "title": "should reset specific analytics data type", "duration": 1.0160450000000765, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should reset analytics data by date range", "status": "passed", "title": "should reset analytics data by date range", "duration": 0.7001390000000356, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should require confirmation for reset operations", "status": "passed", "title": "should require confirmation for reset operations", "duration": 0.619570000000067, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Reset"], "fullName": "Admin Analytics Management Flows Analytics Data Reset should handle confirmed reset operation", "status": "passed", "title": "should handle confirmed reset operation", "duration": 0.5617090000000644, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "System Analytics Dashboard"], "fullName": "Admin Analytics Management Flows System Analytics Dashboard should get comprehensive system analytics", "status": "passed", "title": "should get comprehensive system analytics", "duration": 0.7773089999998319, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "System Analytics Dashboard"], "fullName": "Admin Analytics Management Flows System Analytics Dashboard should get analytics data with date filtering", "status": "passed", "title": "should get analytics data with date filtering", "duration": 0.816396000000168, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Real-time Analytics Monitoring"], "fullName": "Admin Analytics Management Flows Real-time Analytics Monitoring should get real-time system health", "status": "passed", "title": "should get real-time system health", "duration": 0.9953530000000228, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Real-time Analytics Monitoring"], "fullName": "Admin Analytics Management Flows Real-time Analytics Monitoring should handle system health with warnings", "status": "passed", "title": "should handle system health with warnings", "duration": 0.9513959999999315, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Real-time Analytics Monitoring"], "fullName": "Admin Analytics Management Flows Real-time Analytics Monitoring should get live analytics stream", "status": "passed", "title": "should get live analytics stream", "duration": 0.8254640000000109, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Export and Backup"], "fullName": "Admin Analytics Management Flows Analytics Data Export and Backup should export analytics data", "status": "passed", "title": "should export analytics data", "duration": 0.5245079999999689, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Export and Backup"], "fullName": "Admin Analytics Management Flows Analytics Data Export and Backup should get export status", "status": "passed", "title": "should get export status", "duration": 0.44756200000006174, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Data Export and Backup"], "fullName": "Admin Analytics Management Flows Analytics Data Export and Backup should create analytics backup", "status": "passed", "title": "should create analytics backup", "duration": 0.3517300000000887, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Configuration Management"], "fullName": "Admin Analytics Management Flows Analytics Configuration Management should get analytics configuration", "status": "passed", "title": "should get analytics configuration", "duration": 1.1318049999999857, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Analytics Configuration Management"], "fullName": "Admin Analytics Management Flows Analytics Configuration Management should update analytics configuration", "status": "passed", "title": "should update analytics configuration", "duration": 0.838745999999901, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Authorization and Security"], "fullName": "Admin Analytics Management Flows Authorization and Security should require admin authorization for analytics management", "status": "passed", "title": "should require admin authorization for analytics management", "duration": 0.3335370000002058, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Analytics Management Flows", "Authorization and Security"], "fullName": "Admin Analytics Management Flows Authorization and Security should require super admin role for reset operations", "status": "passed", "title": "should require super admin role for reset operations", "duration": 0.28891299999986586, "failureMessages": [], "meta": {}}], "startTime": 1756155731380, "endTime": 1756155731396.3335, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/admin-analytics-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Creation"], "fullName": "Admin Benefit Management Flows Benefit Creation should create a new benefit", "status": "passed", "title": "should create a new benefit", "duration": 6.159478999999919, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Creation"], "fullName": "Admin Benefit Management Flows Benefit Creation should validate required fields for benefit creation", "status": "passed", "title": "should validate required fields for benefit creation", "duration": 0.47736299999996845, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Creation"], "fullName": "Admin Benefit Management Flows Benefit Creation should handle duplicate benefit creation", "status": "passed", "title": "should handle duplicate benefit creation", "duration": 0.5888470000002144, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Updates"], "fullName": "Admin Benefit Management Flows Benefit Updates should update benefit information", "status": "passed", "title": "should update benefit information", "duration": 0.5368909999999687, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Updates"], "fullName": "Admin Benefit Management Flows Benefit Updates should activate/deactivate benefit", "status": "passed", "title": "should activate/deactivate benefit", "duration": 0.5254189999998289, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Updates"], "fullName": "Admin Benefit Management Flows Benefit Updates should handle non-existent benefit update", "status": "passed", "title": "should handle non-existent benefit update", "duration": 0.4701230000000578, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Deletion"], "fullName": "Admin Benefit Management Flows Benefit Deletion should delete benefit", "status": "passed", "title": "should delete benefit", "duration": 0.32189100000005055, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Deletion"], "fullName": "Admin Benefit Management Flows Benefit Deletion should handle deletion of benefit with dependencies", "status": "passed", "title": "should handle deletion of benefit with dependencies", "duration": 4.3281520000000455, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Deletion"], "fullName": "Admin Benefit Management Flows Benefit Deletion should force delete benefit with dependencies", "status": "passed", "title": "should force delete benefit with dependencies", "duration": 1.7684410000001662, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Category Management"], "fullName": "Admin Benefit Management Flows Benefit Category Management should create new benefit category", "status": "passed", "title": "should create new benefit category", "duration": 0.6462649999998575, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Category Management"], "fullName": "Admin Benefit Management Flows Benefit Category Management should update benefit category", "status": "passed", "title": "should update benefit category", "duration": 0.28241899999989073, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Category Management"], "fullName": "Admin Benefit Management Flows Benefit Category Management should delete benefit category", "status": "passed", "title": "should delete benefit category", "duration": 0.3868990000000849, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Category Management"], "fullName": "Admin Benefit Management Flows Benefit Category Management should handle deletion of category with benefits", "status": "passed", "title": "should handle deletion of category with benefits", "duration": 0.31494199999997363, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Verification Management"], "fullName": "Admin Benefit Management Flows Benefit Verification Management should get pending benefit verifications", "status": "passed", "title": "should get pending benefit verifications", "duration": 0.8826279999998405, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Verification Management"], "fullName": "Admin Benefit Management Flows Benefit Verification Management should approve benefit verification", "status": "passed", "title": "should approve benefit verification", "duration": 0.4726509999998143, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Verification Management"], "fullName": "Admin Benefit Management Flows Benefit Verification Management should reject benefit verification", "status": "passed", "title": "should reject benefit verification", "duration": 0.3920530000000326, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Analytics and Reporting"], "fullName": "Admin Benefit Management Flows Benefit Analytics and Reporting should get benefit analytics dashboard", "status": "passed", "title": "should get benefit analytics dashboard", "duration": 0.6425960000001396, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Benefit Management Flows", "Benefit Analytics and Reporting"], "fullName": "Admin Benefit Management Flows Benefit Analytics and Reporting should export benefit data", "status": "passed", "title": "should export benefit data", "duration": 0.4010390000000825, "failureMessages": [], "meta": {}}], "startTime": 1756155731169, "endTime": 1756155731190.4011, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/admin-benefit-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Admin Company Management Flows", "Company Creation"], "fullName": "Admin Company Management Flows Company Creation should create a new company", "status": "passed", "title": "should create a new company", "duration": 3.565343999999868, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Creation"], "fullName": "Admin Company Management Flows Company Creation should validate required fields for company creation", "status": "passed", "title": "should validate required fields for company creation", "duration": 0.807700000000068, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Creation"], "fullName": "Admin Company Management Flows Company Creation should handle duplicate company creation", "status": "passed", "title": "should handle duplicate company creation", "duration": 0.5617650000001504, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Updates"], "fullName": "Admin Company Management Flows Company Updates should update company information", "status": "passed", "title": "should update company information", "duration": 0.5685280000000148, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Updates"], "fullName": "Admin Company Management Flows Company Updates should verify company", "status": "passed", "title": "should verify company", "duration": 0.5669070000001284, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Updates"], "fullName": "Admin Company Management Flows Company Updates should handle non-existent company update", "status": "passed", "title": "should handle non-existent company update", "duration": 0.47417700000005425, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Deletion"], "fullName": "Admin Company Management Flows Company Deletion should delete company", "status": "passed", "title": "should delete company", "duration": 0.3265569999998661, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Deletion"], "fullName": "Admin Company Management Flows Company Deletion should handle deletion of company with dependencies", "status": "passed", "title": "should handle deletion of company with dependencies", "duration": 0.5776319999999942, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Deletion"], "fullName": "Admin Company Management Flows Company Deletion should force delete company with dependencies", "status": "passed", "title": "should force delete company with dependencies", "duration": 1.6094250000001011, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Discovery and Notification"], "fullName": "Admin Company Management Flows Company Discovery and Notification should run discover and notify for matching domain", "status": "passed", "title": "should run discover and notify for matching domain", "duration": 0.7379909999999654, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Discovery and Notification"], "fullName": "Admin Company Management Flows Company Discovery and Notification should handle discovery with no matching users", "status": "passed", "title": "should handle discovery with no matching users", "duration": 0.6182019999998829, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Analytics and Reporting"], "fullName": "Admin Company Management Flows Company Analytics and Reporting should get company analytics dashboard", "status": "passed", "title": "should get company analytics dashboard", "duration": 0.7518649999999525, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Company Analytics and Reporting"], "fullName": "Admin Company Management Flows Company Analytics and Reporting should export company data", "status": "passed", "title": "should export company data", "duration": 0.516542999999956, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Authorization and Security"], "fullName": "Admin Company Management Flows Authorization and Security should require admin authorization", "status": "passed", "title": "should require admin authorization", "duration": 0.5858040000000528, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin Company Management Flows", "Authorization and Security"], "fullName": "Admin Company Management Flows Authorization and Security should require admin role", "status": "passed", "title": "should require admin role", "duration": 0.36860300000012103, "failureMessages": [], "meta": {}}], "startTime": 1756155731349, "endTime": 1756155731363.3687, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/admin-company-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Admin User Management Flows", "User Listing and Search"], "fullName": "Admin User Management Flows User Listing and Search should get all users with pagination", "status": "passed", "title": "should get all users with pagination", "duration": 2.5972620000000006, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Listing and Search"], "fullName": "Admin User Management Flows User Listing and Search should search users by email", "status": "passed", "title": "should search users by email", "duration": 0.45667900000012196, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Listing and Search"], "fullName": "Admin User Management Flows User Listing and Search should filter users by company", "status": "passed", "title": "should filter users by company", "duration": 0.3549799999998413, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Listing and Search"], "fullName": "Admin User Management Flows User Listing and Search should filter users by role", "status": "passed", "title": "should filter users by role", "duration": 0.3385990000001584, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Details and Profile Management"], "fullName": "Admin User Management Flows User Details and Profile Management should get user details", "status": "passed", "title": "should get user details", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Details and Profile Management"], "fullName": "Admin User Management Flows User Details and Profile Management should handle non-existent user", "status": "passed", "title": "should handle non-existent user", "duration": 0.*****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Account Management"], "fullName": "Admin User Management Flows User Account Management should update user information", "status": "passed", "title": "should update user information", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Account Management"], "fullName": "Admin User Management Flows User Account Management should deactivate user account", "status": "passed", "title": "should deactivate user account", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Account Management"], "fullName": "Admin User Management Flows User Account Management should reactivate user account", "status": "passed", "title": "should reactivate user account", "duration": 0.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Account Management"], "fullName": "Admin User Management Flows User Account Management should delete user account", "status": "passed", "title": "should delete user account", "duration": 0.***************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Company Associations"], "fullName": "Admin User Management Flows User Company Associations should update user company association", "status": "passed", "title": "should update user company association", "duration": 0.*****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Company Associations"], "fullName": "Admin User Management Flows User Company Associations should remove user company association", "status": "passed", "title": "should remove user company association", "duration": 0.*****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Analytics and Activity"], "fullName": "Admin User Management Flows User Analytics and Activity should get user analytics dashboard", "status": "passed", "title": "should get user analytics dashboard", "duration": 0.34724899999991976, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Analytics and Activity"], "fullName": "Admin User Management Flows User Analytics and Activity should get user activity timeline", "status": "passed", "title": "should get user activity timeline", "duration": 0.548225999999886, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "User Analytics and Activity"], "fullName": "Admin User Management Flows User Analytics and Activity should export user data", "status": "passed", "title": "should export user data", "duration": 0.3352270000000317, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "Authorization and Security"], "fullName": "Admin User Management Flows Authorization and Security should require admin authorization", "status": "passed", "title": "should require admin authorization", "duration": 0.14853299999981573, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "Authorization and Security"], "fullName": "Admin User Management Flows Authorization and Security should require admin role", "status": "passed", "title": "should require admin role", "duration": 0.14838099999997212, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Admin User Management Flows", "Authorization and Security"], "fullName": "Admin User Management Flows Authorization and Security should prevent admin from deleting themselves", "status": "passed", "title": "should prevent admin from deleting themselves", "duration": 0.13111000000003514, "failureMessages": [], "meta": {}}], "startTime": 1756155731204, "endTime": 1756155731212.3352, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/admin-user-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Simple Analytics System", "Basic Analytics Tracking"], "fullName": "Simple Analytics System Basic Analytics Tracking should track page views without authentication", "status": "passed", "title": "should track page views without authentication", "duration": 6.770573999999897, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Basic Analytics Tracking"], "fullName": "Simple Analytics System Basic Analytics Tracking should track search queries", "status": "passed", "title": "should track search queries", "duration": 0.9828170000000682, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Basic Analytics Tracking"], "fullName": "Simple Analytics System Basic Analytics Tracking should track company profile views", "status": "passed", "title": "should track company profile views", "duration": 0.6659230000000207, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Analytics Data Validation"], "fullName": "Simple Analytics System Analytics Data Validation should validate required fields in tracking data", "status": "passed", "title": "should validate required fields in tracking data", "duration": 0.7227660000000924, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Analytics Data Validation"], "fullName": "Simple Analytics System Analytics Data Validation should validate tracking data format", "status": "passed", "title": "should validate tracking data format", "duration": 0.3730320000001939, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Rate Limiting"], "fullName": "Simple Analytics System Rate Limiting should handle rate limiting for analytics tracking", "status": "passed", "title": "should handle rate limiting for analytics tracking", "duration": 0.624953000000005, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Analytics Aggregation"], "fullName": "Simple Analytics System Analytics Aggregation should aggregate daily analytics data", "status": "passed", "title": "should aggregate daily analytics data", "duration": 0.5416079999999965, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Analytics Aggregation"], "fullName": "Simple Analytics System Analytics Aggregation should provide weekly analytics trends", "status": "passed", "title": "should provide weekly analytics trends", "duration": 0.39507499999990614, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Erro<PERSON>"], "fullName": "Simple Analytics System Error Handling should handle network errors gracefully", "status": "passed", "title": "should handle network errors gracefully", "duration": 0.9367239999999128, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Analytics System", "Erro<PERSON>"], "fullName": "Simple Analytics System Error Handling should handle malformed JSON responses", "status": "passed", "title": "should handle malformed JSON responses", "duration": 0.43897599999991144, "failureMessages": [], "meta": {}}], "startTime": 1756155731349, "endTime": 1756155731362.439, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/analytics-simple.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Analytics System Validation", "Database Schema Validation"], "fullName": "Analytics System Validation Database Schema Validation should validate analytics tables exist", "status": "passed", "title": "should validate analytics tables exist", "duration": 1.531994999999938, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Database Schema Validation"], "fullName": "Analytics System Validation Database Schema Validation should validate analytics table columns", "status": "passed", "title": "should validate analytics table columns", "duration": 0.49027300000000196, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "API Endpoint Validation"], "fullName": "Analytics System Validation API Endpoint Validation should validate analytics tracking endpoint", "status": "passed", "title": "should validate analytics tracking endpoint", "duration": 0.6978440000000319, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "API Endpoint Validation"], "fullName": "Analytics System Validation API Endpoint Validation should validate analytics insights endpoint", "status": "passed", "title": "should validate analytics insights endpoint", "duration": 0.714525999999978, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "API Endpoint Validation"], "fullName": "Analytics System Validation API Endpoint Validation should validate admin analytics endpoints", "status": "passed", "title": "should validate admin analytics endpoints", "duration": 0.24438099999997576, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Data Integrity Validation"], "fullName": "Analytics System Validation Data Integrity Validation should validate analytics data consistency", "status": "passed", "title": "should validate analytics data consistency", "duration": 0.28847400000006473, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Data Integrity Validation"], "fullName": "Analytics System Validation Data Integrity Validation should validate analytics aggregation accuracy", "status": "passed", "title": "should validate analytics aggregation accuracy", "duration": 0.10129400000005262, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Performance Validation"], "fullName": "Analytics System Validation Performance Validation should validate analytics query performance", "status": "passed", "title": "should validate analytics query performance", "duration": 0.202274999999986, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Performance Validation"], "fullName": "Analytics System Validation Performance Validation should validate analytics data volume handling", "status": "passed", "title": "should validate analytics data volume handling", "duration": 0.18127400000003036, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Security Validation"], "fullName": "Analytics System Validation Security Validation should validate authentication requirements", "status": "passed", "title": "should validate authentication requirements", "duration": 0.15817300000003343, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Security Validation"], "fullName": "Analytics System Validation Security Validation should validate authorization for admin endpoints", "status": "passed", "title": "should validate authorization for admin endpoints", "duration": 0.11088900000004287, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Error Handling Validation"], "fullName": "Analytics System Validation Error Handling Validation should validate graceful error handling", "status": "passed", "title": "should validate graceful error handling", "duration": 0.10522600000001603, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Error Handling Validation"], "fullName": "Analytics System Validation Error Handling Validation should validate input validation", "status": "passed", "title": "should validate input validation", "duration": 0.13796800000000076, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System Validation", "Integration Validation"], "fullName": "Analytics System Validation Integration Validation should validate end-to-end analytics flow", "status": "passed", "title": "should validate end-to-end analytics flow", "duration": 0.2517850000000408, "failureMessages": [], "meta": {}}], "startTime": 1756155732279, "endTime": 1756155732285.2517, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/analytics-validation.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Analytics System", "Analytics Tracking"], "fullName": "Analytics System Analytics Tracking should track company views", "status": "passed", "title": "should track company views", "duration": 2.907452000000035, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Analytics Tracking"], "fullName": "Analytics System Analytics Tracking should track benefit searches", "status": "passed", "title": "should track benefit searches", "duration": 0.42244300000004387, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Analytics Tracking"], "fullName": "Analytics System Analytics Tracking should track user interactions", "status": "passed", "title": "should track user interactions", "duration": 0.3439539999999397, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Analytics Data Retrieval"], "fullName": "Analytics System Analytics Data Retrieval should retrieve analytics insights for paying users", "status": "passed", "title": "should retrieve analytics insights for paying users", "duration": 0.27459399999997913, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Analytics Data Retrieval"], "fullName": "Analytics System Analytics Data Retrieval should return preview data for non-paying users", "status": "passed", "title": "should return preview data for non-paying users", "duration": 0.1410680000000184, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Admin Analytics"], "fullName": "Analytics System Admin Analytics should allow admin to reset analytics data", "status": "passed", "title": "should allow admin to reset analytics data", "duration": 0.25401499999998123, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Admin Analytics"], "fullName": "Analytics System Admin Analytics should provide admin analytics dashboard data", "status": "passed", "title": "should provide admin analytics dashboard data", "duration": 0.13628600000004099, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Erro<PERSON>"], "fullName": "Analytics System Error Handling should handle invalid tracking data", "status": "passed", "title": "should handle invalid tracking data", "duration": 0.13386300000001938, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Analytics System", "Erro<PERSON>"], "fullName": "Analytics System Error Handling should handle unauthorized access to admin endpoints", "status": "passed", "title": "should handle unauthorized access to admin endpoints", "duration": 0.*****************, "failureMessages": [], "meta": {}}], "startTime": *************, "endTime": *************.254, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/analytics.test.ts"}, {"assertionResults": [{"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/sign-up"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/sign-up should create new user account", "status": "passed", "title": "should create new user account", "duration": 1.****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/sign-up"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/sign-up should validate email format", "status": "passed", "title": "should validate email format", "duration": 0.*****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/sign-in"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/sign-in should send magic link for existing user", "status": "passed", "title": "should send magic link for existing user", "duration": 0.*****************, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/sign-in"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/sign-in should handle non-existent user", "status": "passed", "title": "should handle non-existent user", "duration": 0.33682100000009996, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/magic-link"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/magic-link should verify valid magic link", "status": "passed", "title": "should verify valid magic link", "duration": 0.29555900000013935, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Authentication Endpoints", "POST /api/auth/magic-link"], "fullName": "API Integration Tests Authentication Endpoints POST /api/auth/magic-link should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.17558099999996557, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "GET /api/companies"], "fullName": "API Integration Tests Company Endpoints GET /api/companies should return paginated companies", "status": "passed", "title": "should return paginated companies", "duration": 0.9735580000001391, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "GET /api/companies"], "fullName": "API Integration Tests Company Endpoints GET /api/companies should filter companies by search query", "status": "passed", "title": "should filter companies by search query", "duration": 0.19472700000005716, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "GET /api/companies/[id]"], "fullName": "API Integration Tests Company Endpoints GET /api/companies/[id] should return company details", "status": "passed", "title": "should return company details", "duration": 0.2858569999998508, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "GET /api/companies/[id]"], "fullName": "API Integration Tests Company Endpoints GET /api/companies/[id] should handle non-existent company", "status": "passed", "title": "should handle non-existent company", "duration": 0.17719399999987218, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "POST /api/companies/[id]/benefits"], "fullName": "API Integration Tests Company Endpoints POST /api/companies/[id]/benefits should add benefit to company (authenticated)", "status": "passed", "title": "should add benefit to company (authenticated)", "duration": 0.2226980000000367, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Company Endpoints", "POST /api/companies/[id]/benefits"], "fullName": "API Integration Tests Company Endpoints POST /api/companies/[id]/benefits should require authentication", "status": "passed", "title": "should require authentication", "duration": 0.13853500000004715, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Benefit Endpoints", "GET /api/benefits"], "fullName": "API Integration Tests Benefit Endpoints GET /api/benefits should return all benefits", "status": "passed", "title": "should return all benefits", "duration": 0.27112000000010994, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Benefit Endpoints", "GET /api/benefits"], "fullName": "API Integration Tests Benefit Endpoints GET /api/benefits should filter benefits by category", "status": "passed", "title": "should filter benefits by category", "duration": 0.3690470000001369, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Benefit Endpoints", "GET /api/benefits/[id]"], "fullName": "API Integration Tests Benefit Endpoints GET /api/benefits/[id] should return benefit details", "status": "passed", "title": "should return benefit details", "duration": 0.1969930000000204, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "User Endpoints", "GET /api/user/profile"], "fullName": "API Integration Tests User Endpoints GET /api/user/profile should return user profile (authenticated)", "status": "passed", "title": "should return user profile (authenticated)", "duration": 0.18364500000006956, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "User Endpoints", "GET /api/user/profile"], "fullName": "API Integration Tests User Endpoints GET /api/user/profile should require authentication", "status": "passed", "title": "should require authentication", "duration": 0.10499099999992723, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "User Endpoints", "POST /api/user/benefit-ranking"], "fullName": "API Integration Tests User Endpoints POST /api/user/benefit-ranking should add benefit to ranking (authenticated)", "status": "passed", "title": "should add benefit to ranking (authenticated)", "duration": 0.1211950000001707, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "User Endpoints", "GET /api/user/saved-companies"], "fullName": "API Integration Tests User Endpoints GET /api/user/saved-companies should return saved companies (authenticated)", "status": "passed", "title": "should return saved companies (authenticated)", "duration": 0.20073100000013255, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "GET /api/admin/companies"], "fullName": "API Integration Tests Admin Endpoints GET /api/admin/companies should return companies for admin", "status": "passed", "title": "should return companies for admin", "duration": 0.1930970000000798, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "GET /api/admin/companies"], "fullName": "API Integration Tests Admin Endpoints GET /api/admin/companies should require admin role", "status": "passed", "title": "should require admin role", "duration": 0.0982720000001791, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "POST /api/admin/companies"], "fullName": "API Integration Tests Admin Endpoints POST /api/admin/companies should create company (admin)", "status": "passed", "title": "should create company (admin)", "duration": 0.10074099999997088, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "DELETE /api/admin/companies/[id]"], "fullName": "API Integration Tests Admin Endpoints DELETE /api/admin/companies/[id] should delete company (admin)", "status": "passed", "title": "should delete company (admin)", "duration": 0.10006999999995969, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "POST /api/admin/analytics/reset"], "fullName": "API Integration Tests Admin Endpoints POST /api/admin/analytics/reset should reset analytics (super admin)", "status": "passed", "title": "should reset analytics (super admin)", "duration": 0.08451800000011644, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Admin Endpoints", "POST /api/admin/analytics/reset"], "fullName": "API Integration Tests Admin Endpoints POST /api/admin/analytics/reset should require super admin role", "status": "passed", "title": "should require super admin role", "duration": 0.07850700000017241, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Analytics Endpoints", "POST /api/analytics/track"], "fullName": "API Integration Tests Analytics Endpoints POST /api/analytics/track should track analytics event", "status": "passed", "title": "should track analytics event", "duration": 0.09273800000005394, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Analytics Endpoints", "POST /api/analytics/track"], "fullName": "API Integration Tests Analytics Endpoints POST /api/analytics/track should validate tracking data", "status": "passed", "title": "should validate tracking data", "duration": 0.098212999999987, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Analytics Endpoints", "GET /api/analytics/insights"], "fullName": "API Integration Tests Analytics Endpoints GET /api/analytics/insights should return insights for premium users", "status": "passed", "title": "should return insights for premium users", "duration": 0.08379200000013043, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Analytics Endpoints", "GET /api/analytics/insights"], "fullName": "API Integration Tests Analytics Endpoints GET /api/analytics/insights should return preview for non-premium users", "status": "passed", "title": "should return preview for non-premium users", "duration": 0.08021100000019032, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Erro<PERSON>"], "fullName": "API Integration Tests Error Handling should handle 404 for non-existent endpoints", "status": "passed", "title": "should handle 404 for non-existent endpoints", "duration": 0.07702500000004875, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Erro<PERSON>"], "fullName": "API Integration Tests Error Handling should handle 405 for unsupported methods", "status": "passed", "title": "should handle 405 for unsupported methods", "duration": 0.0740650000000187, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Erro<PERSON>"], "fullName": "API Integration Tests Error Handling should handle 500 for server errors", "status": "passed", "title": "should handle 500 for server errors", "duration": 0.06955500000003667, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["API Integration Tests", "Erro<PERSON>"], "fullName": "API Integration Tests Error Handling should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 0.08537599999999657, "failureMessages": [], "meta": {}}], "startTime": 1756155731206, "endTime": 1756155731215.0854, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/api-integration.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Database Location Functions", "getCompanyLocations"], "fullName": "Database Location Functions getCompanyLocations should fetch company locations ordered correctly", "status": "passed", "title": "should fetch company locations ordered correctly", "duration": 6.185907999999927, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "getCompanyLocations"], "fullName": "Database Location Functions getCompanyLocations should return empty array for company with no locations", "status": "passed", "title": "should return empty array for company with no locations", "duration": 0.5199929999998858, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "addCompanyLocation"], "fullName": "Database Location Functions addCompanyLocation should add a new company location", "status": "passed", "title": "should add a new company location", "duration": 1.6443989999997939, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "addCompanyLocation"], "fullName": "Database Location Functions addCompanyLocation should unset other primary locations when setting as primary", "status": "passed", "title": "should unset other primary locations when setting as primary", "duration": 3.7576030000000173, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "addCompanyLocation"], "fullName": "Database Location Functions addCompanyLocation should unset other headquarters when setting as headquarters", "status": "passed", "title": "should unset other headquarters when setting as headquarters", "duration": 0.5960879999997815, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "updateCompanyLocation"], "fullName": "Database Location Functions updateCompanyLocation should update location with new data", "status": "passed", "title": "should update location with new data", "duration": 16.15250900000001, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "updateCompanyLocation"], "fullName": "Database Location Functions updateCompanyLocation should throw error for non-existent location", "status": "passed", "title": "should throw error for non-existent location", "duration": 2.1514259999999013, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "removeCompanyLocation"], "fullName": "Database Location Functions removeCompanyLocation should remove company location", "status": "passed", "title": "should remove company location", "duration": 2.422587999999905, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Database Location Functions", "removeCompanyLocation"], "fullName": "Database Location Functions removeCompanyLocation should throw error for non-existent location", "status": "passed", "title": "should throw error for non-existent location", "duration": 0.49904700000001867, "failureMessages": [], "meta": {}}], "startTime": 1756155731487, "endTime": 1756155731521.499, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/database-locations.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should normalize German city names", "status": "passed", "title": "should normalize German city names", "duration": 220.57685300000003, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should normalize English city names", "status": "passed", "title": "should normalize English city names", "duration": 19.01950399999987, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should handle cities with country already specified", "status": "passed", "title": "should handle cities with country already specified", "duration": 20.462935000000016, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should use database mapping when available", "status": "skipped", "title": "should use database mapping when available", "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should handle empty location", "status": "passed", "title": "should handle empty location", "duration": 2.998017000000118, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should detect German cities by patterns", "status": "passed", "title": "should detect German cities by patterns", "duration": 37.309258999999884, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should handle international cities", "status": "passed", "title": "should handle international cities", "duration": 17.47844199999986, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "normalizeLocation"], "fullName": "Location Normalization normalizeLocation should handle unknown locations gracefully", "status": "passed", "title": "should handle unknown locations gracefully", "duration": 37.940423999999894, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should return empty array for short queries", "status": "passed", "title": "should return empty array for short queries", "duration": 1.2492789999996603, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should return database suggestions when available", "status": "passed", "title": "should return database suggestions when available", "duration": 0.8936559999997371, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should return database suggestions with correct format", "status": "passed", "title": "should return database suggestions with correct format", "duration": 0.5355490000001737, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should limit results correctly", "status": "passed", "title": "should limit results correctly", "duration": 0.2923710000000028, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Location Normalization", "getLocationSuggestions"], "fullName": "Location Normalization getLocationSuggestions should avoid duplicates", "status": "passed", "title": "should avoid duplicates", "duration": 0.30311299999993935, "failureMessages": [], "meta": {}}], "startTime": 1756155731670, "endTime": 1756155732030.3032, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/location-normalization.test.ts"}, {"assertionResults": [{"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should set cache successfully", "status": "passed", "title": "should set cache successfully", "duration": 13.07809300000008, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should get cache successfully", "status": "passed", "title": "should get cache successfully", "duration": 2.37112100000013, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should return null for non-existent cache", "status": "passed", "title": "should return null for non-existent cache", "duration": 1.3473690000000715, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should delete cache successfully", "status": "passed", "title": "should delete cache successfully", "duration": 1.6691359999999804, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Basic Cache Operations"], "fullName": "PostgreSQL Cache System Basic Cache Operations should clear cache pattern successfully", "status": "passed", "title": "should clear cache pattern successfully", "duration": 1.6401089999999385, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Session Management"], "fullName": "PostgreSQL Cache System Session Management should set session successfully", "status": "passed", "title": "should set session successfully", "duration": 12.148081999999931, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Session Management"], "fullName": "PostgreSQL Cache System Session Management should get session successfully", "status": "passed", "title": "should get session successfully", "duration": 1.6682090000001608, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Session Management"], "fullName": "PostgreSQL Cache System Session Management should delete session successfully", "status": "passed", "title": "should delete session successfully", "duration": 3.0504770000000008, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "CSRF Token Management"], "fullName": "PostgreSQL Cache System CSRF Token Management should set CSRF token successfully", "status": "passed", "title": "should set CSRF token successfully", "duration": 2.684377999999924, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "CSRF Token Management"], "fullName": "PostgreSQL Cache System CSRF Token Management should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 1.8346710000000712, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Health and Stats"], "fullName": "PostgreSQL Cache System Health and Stats should check cache health successfully", "status": "passed", "title": "should check cache health successfully", "duration": 1.79319600000008, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Health and Stats"], "fullName": "PostgreSQL Cache System Health and Stats should get cache stats successfully", "status": "passed", "title": "should get cache stats successfully", "duration": 1.0869179999999687, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Erro<PERSON>"], "fullName": "PostgreSQL Cache System Error Handling should handle cache set errors gracefully", "status": "passed", "title": "should handle cache set errors gracefully", "duration": 14.280571000000009, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Erro<PERSON>"], "fullName": "PostgreSQL Cache System Error Handling should handle cache get errors gracefully", "status": "passed", "title": "should handle cache get errors gracefully", "duration": 7.2979109999998855, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Cache System", "Erro<PERSON>"], "fullName": "PostgreSQL Cache System Error Handling should handle health check errors gracefully", "status": "passed", "title": "should handle health check errors gracefully", "duration": 2.509595999999874, "failureMessages": [], "meta": {}}], "startTime": 1756155731508, "endTime": 1756155731578.5095, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/postgresql-cache.test.ts"}, {"assertionResults": [{"ancestorTitles": ["PostgreSQL Rate Limiting", "Basic Rate Limiting"], "fullName": "PostgreSQL Rate Limiting Basic Rate Limiting should allow request when under limit", "status": "passed", "title": "should allow request when under limit", "duration": 3.4458029999998416, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Basic Rate Limiting"], "fullName": "PostgreSQL Rate Limiting Basic Rate Limiting should deny request when over limit", "status": "passed", "title": "should deny request when over limit", "duration": 1.1192389999998795, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Basic Rate Limiting"], "fullName": "PostgreSQL Rate Limiting Basic Rate Limiting should handle database errors gracefully", "status": "passed", "title": "should handle database errors gracefully", "duration": 13.770447000000104, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Sliding Window Rate Limiting"], "fullName": "PostgreSQL Rate Limiting Sliding Window Rate Limiting should work with sliding window", "status": "passed", "title": "should work with sliding window", "duration": 1.0945050000000265, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Convenience Functions"], "fullName": "PostgreSQL Rate Limiting Convenience Functions should check API rate limit", "status": "passed", "title": "should check API rate limit", "duration": 0.6528880000000754, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Convenience Functions"], "fullName": "PostgreSQL Rate Limiting Convenience Functions should check auth rate limit", "status": "passed", "title": "should check auth rate limit", "duration": 2.4562009999999646, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Maintenance Functions"], "fullName": "PostgreSQL Rate Limiting Maintenance Functions should cleanup expired rate limits", "status": "passed", "title": "should cleanup expired rate limits", "duration": 9.22888399999988, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Maintenance Functions"], "fullName": "PostgreSQL Rate Limiting Maintenance Functions should get rate limit stats", "status": "passed", "title": "should get rate limit stats", "duration": 2.118932000000086, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Maintenance Functions"], "fullName": "PostgreSQL Rate Limiting Maintenance Functions should reset rate limit for user", "status": "passed", "title": "should reset rate limit for user", "duration": 2.018481999999949, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Edge Cases"], "fullName": "PostgreSQL Rate Limiting Edge Cases should handle empty timestamps array", "status": "passed", "title": "should handle empty timestamps array", "duration": 0.8051820000000589, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Edge Cases"], "fullName": "PostgreSQL Rate Limiting Edge Cases should handle malformed timestamps", "status": "passed", "title": "should handle malformed timestamps", "duration": 3.8404810000001817, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["PostgreSQL Rate Limiting", "Edge Cases"], "fullName": "PostgreSQL Rate Limiting Edge Cases should filter out old timestamps", "status": "passed", "title": "should filter out old timestamps", "duration": 0.6460859999999684, "failureMessages": [], "meta": {}}], "startTime": 1756155731505, "endTime": 1756155731547.646, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/postgresql-rate-limit.test.ts"}, {"assertionResults": [{"ancestorTitles": ["SavedCompaniesPage"], "fullName": "SavedCompaniesPage should render without TDZ errors", "status": "passed", "title": "should render without TDZ errors", "duration": 87.88091000000009, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["SavedCompaniesPage"], "fullName": "SavedCompaniesPage should handle successful authentication and fetch saved companies", "status": "passed", "title": "should handle successful authentication and fetch saved companies", "duration": 21.30886200000009, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["SavedCompaniesPage"], "fullName": "SavedCompaniesPage should handle empty saved companies list", "status": "passed", "title": "should handle empty saved companies list", "duration": 15.27811500000007, "failureMessages": [], "meta": {}}], "startTime": 1756155731676, "endTime": 1756155731800.278, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/saved-companies-page.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["User Analytics and Insights Flows", "Premium Analytics Insights"], "fullName": "User Analytics and Insights Flows Premium Analytics Insights should provide full analytics insights for premium users", "status": "passed", "title": "should provide full analytics insights for premium users", "duration": 3.4727479999999105, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Premium Analytics Insights"], "fullName": "User Analytics and Insights Flows Premium Analytics Insights should provide detailed benefit ranking analytics for premium users", "status": "passed", "title": "should provide detailed benefit ranking analytics for premium users", "duration": 0.8229199999999537, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Premium Analytics Insights"], "fullName": "User Analytics and Insights Flows Premium Analytics Insights should provide company comparison analytics for premium users", "status": "passed", "title": "should provide company comparison analytics for premium users", "duration": 0.6936709999999948, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Non-Premium Preview Data"], "fullName": "User Analytics and Insights Flows Non-Premium Preview Data should provide limited preview data for non-premium users", "status": "passed", "title": "should provide limited preview data for non-premium users", "duration": 0.5756639999999607, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Non-Premium Preview Data"], "fullName": "User Analytics and Insights Flows Non-Premium Preview Data should show upgrade prompt for detailed analytics", "status": "passed", "title": "should show upgrade prompt for detailed analytics", "duration": 0.37587200000007215, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Non-Premium Preview Data"], "fullName": "User Analytics and Insights Flows Non-Premium Preview Data should limit data export for non-premium users", "status": "passed", "title": "should limit data export for non-premium users", "duration": 0.35365999999999076, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Interaction Tracking"], "fullName": "User Analytics and Insights Flows Analytics Interaction Tracking should track analytics page views", "status": "passed", "title": "should track analytics page views", "duration": 0.25383399999998346, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Interaction Tracking"], "fullName": "User Analytics and Insights Flows Analytics Interaction Tracking should track insight interactions", "status": "passed", "title": "should track insight interactions", "duration": 0.20655699999997523, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Interaction Tracking"], "fullName": "User Analytics and Insights Flows Analytics Interaction Tracking should track upgrade prompt interactions", "status": "passed", "title": "should track upgrade prompt interactions", "duration": 0.3004910000000791, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Data Refresh"], "fullName": "User Analytics and Insights Flows Analytics Data Refresh should refresh user analytics data", "status": "passed", "title": "should refresh user analytics data", "duration": 0.6384580000000142, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Analytics and Insights Flows", "Analytics Data Refresh"], "fullName": "User Analytics and Insights Flows Analytics Data Refresh should handle analytics refresh rate limiting", "status": "passed", "title": "should handle analytics refresh rate limiting", "duration": 0.29134800000008454, "failureMessages": [], "meta": {}}], "startTime": 1756155732147, "endTime": 1756155732156.2913, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/user-analytics-insights.test.ts"}, {"assertionResults": [{"ancestorTitles": ["User Authentication Flows", "User Registration Flow"], "fullName": "User Authentication Flows User Registration Flow should handle user sign up with email", "status": "passed", "title": "should handle user sign up with email", "duration": 5.802796000000171, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Registration Flow"], "fullName": "User Authentication Flows User Registration Flow should handle duplicate email registration", "status": "passed", "title": "should handle duplicate email registration", "duration": 0.8218549999999141, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Registration Flow"], "fullName": "User Authentication Flows User Registration Flow should validate required fields", "status": "passed", "title": "should validate required fields", "duration": 0.5226849999999104, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Sign In Flow"], "fullName": "User Authentication Flows User Sign In Flow should handle user sign in with email", "status": "passed", "title": "should handle user sign in with email", "duration": 0.49611400000003414, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Sign In Flow"], "fullName": "User Authentication Flows User Sign In Flow should handle non-existent email", "status": "passed", "title": "should handle non-existent email", "duration": 0.5120320000000902, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Sign In Flow"], "fullName": "User Authentication Flows User Sign In Flow should handle rate limiting for sign in attempts", "status": "passed", "title": "should handle rate limiting for sign in attempts", "duration": 0.5061329999998634, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Magic Link Verification Flow"], "fullName": "User Authentication Flows Magic Link Verification Flow should verify valid magic link token", "status": "passed", "title": "should verify valid magic link token", "duration": 0.48574200000007295, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Magic Link Verification Flow"], "fullName": "User Authentication Flows Magic Link Verification Flow should handle expired magic link token", "status": "passed", "title": "should handle expired magic link token", "duration": 0.3350780000000668, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Magic Link Verification Flow"], "fullName": "User Authentication Flows Magic Link Verification Flow should handle invalid magic link token", "status": "passed", "title": "should handle invalid magic link token", "duration": 0.4719560000000911, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "User Sign Out Flow"], "fullName": "User Authentication Flows User Sign Out Flow should handle user sign out", "status": "passed", "title": "should handle user sign out", "duration": 0.4921919999999318, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Profile Update Flow"], "fullName": "User Authentication Flows Profile Update Flow should update user profile information", "status": "passed", "title": "should update user profile information", "duration": 0.45057999999994536, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Authentication Flows", "Profile Update Flow"], "fullName": "User Authentication Flows Profile Update Flow should handle unauthorized profile update", "status": "passed", "title": "should handle unauthorized profile update", "duration": 0.42157700000007026, "failureMessages": [], "meta": {}}], "startTime": 1756155731350, "endTime": 1756155731362.4216, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/user-authentication.test.ts"}, {"assertionResults": [{"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should add benefit to personal ranking", "status": "passed", "title": "should add benefit to personal ranking", "duration": 3.0509360000000925, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should remove benefit from personal ranking", "status": "passed", "title": "should remove benefit from personal ranking", "duration": 0.6893000000000029, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should reorder benefits in personal ranking", "status": "passed", "title": "should reorder benefits in personal ranking", "duration": 4.645312999999987, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should reset personal benefit ranking", "status": "passed", "title": "should reset personal benefit ranking", "duration": 0.6158909999999196, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Personal Benefit Ranking"], "fullName": "User Benefit Management Flows Personal Benefit Ranking should get user benefit ranking", "status": "passed", "title": "should get user benefit ranking", "duration": 0.9565880000000107, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Company Benefit Management"], "fullName": "User Benefit Management Flows Company Benefit Management should add benefit to company benefit list", "status": "passed", "title": "should add benefit to company benefit list", "duration": 0.8028689999998733, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Company Benefit Management"], "fullName": "User Benefit Management Flows Company Benefit Management should handle unauthorized benefit addition", "status": "passed", "title": "should handle unauthorized benefit addition", "duration": 0.46951899999999114, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Benefit Verification and Disputes"], "fullName": "User Benefit Management Flows Benefit Verification and Disputes should create benefit verification (confirm)", "status": "passed", "title": "should create benefit verification (confirm)", "duration": 0.4427389999998468, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Benefit Verification and Disputes"], "fullName": "User Benefit Management Flows Benefit Verification and Disputes should create benefit dispute", "status": "passed", "title": "should create benefit dispute", "duration": 0.6338490000000547, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Benefit Verification and Disputes"], "fullName": "User Benefit Management Flows Benefit Verification and Disputes should check authorization for benefit verification", "status": "passed", "title": "should check authorization for benefit verification", "duration": 0.41144099999996797, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Benefit Verification and Disputes"], "fullName": "User Benefit Management Flows Benefit Verification and Disputes should handle unauthorized verification attempt", "status": "passed", "title": "should handle unauthorized verification attempt", "duration": 0.35264299999994364, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Browse Benefits"], "fullName": "User Benefit Management Flows Browse Benefits should get all available benefits", "status": "passed", "title": "should get all available benefits", "duration": 0.7456599999998161, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Browse Benefits"], "fullName": "User Benefit Management Flows Browse Benefits should filter benefits by category", "status": "passed", "title": "should filter benefits by category", "duration": 0.5134259999999813, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Benefit Management Flows", "Browse Benefits"], "fullName": "User Benefit Management Flows Browse Benefits should search benefits by name", "status": "passed", "title": "should search benefits by name", "duration": 0.8024199999999837, "failureMessages": [], "meta": {}}], "startTime": 1756155731371, "endTime": 1756155731387.8025, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/user-benefit-management.test.ts"}, {"assertionResults": [{"ancestorTitles": ["User Company Interaction Flows", "Company Search and Discovery"], "fullName": "User Company Interaction Flows Company Search and Discovery should search companies by name", "status": "passed", "title": "should search companies by name", "duration": 2.3057469999999967, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Search and Discovery"], "fullName": "User Company Interaction Flows Company Search and Discovery should filter companies by location", "status": "passed", "title": "should filter companies by location", "duration": 0.3248549999999568, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Search and Discovery"], "fullName": "User Company Interaction Flows Company Search and Discovery should filter companies by benefits", "status": "passed", "title": "should filter companies by benefits", "duration": 0.45584999999994125, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Search and Discovery"], "fullName": "User Company Interaction Flows Company Search and Discovery should handle empty search results", "status": "passed", "title": "should handle empty search results", "duration": 0.24327099999993607, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Profile Viewing"], "fullName": "User Company Interaction Flows Company Profile Viewing should get company profile details", "status": "passed", "title": "should get company profile details", "duration": 0.31231400000001486, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Company Profile Viewing"], "fullName": "User Company Interaction Flows Company Profile Viewing should handle non-existent company", "status": "passed", "title": "should handle non-existent company", "duration": 0.18878899999992882, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Saved Companies Management"], "fullName": "User Company Interaction Flows Saved Companies Management should save a company to user favorites", "status": "passed", "title": "should save a company to user favorites", "duration": 0.3232900000000427, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Saved Companies Management"], "fullName": "User Company Interaction Flows Saved Companies Management should get user saved companies", "status": "passed", "title": "should get user saved companies", "duration": 0.1975759999999127, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Saved Companies Management"], "fullName": "User Company Interaction Flows Saved Companies Management should delete saved company", "status": "passed", "title": "should delete saved company", "duration": 0.22261300000002393, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Saved Companies Management"], "fullName": "User Company Interaction Flows Saved Companies Management should handle duplicate save attempt", "status": "passed", "title": "should handle duplicate save attempt", "duration": 0.19589499999995041, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Missing Company Reporting"], "fullName": "User Company Interaction Flows Missing Company Reporting should report missing company", "status": "passed", "title": "should report missing company", "duration": 0.16469999999992524, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Missing Company Reporting"], "fullName": "User Company Interaction Flows Missing Company Reporting should validate required fields for missing company report", "status": "passed", "title": "should validate required fields for missing company report", "duration": 0.15518300000007912, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["User Company Interaction Flows", "Missing Company Reporting"], "fullName": "User Company Interaction Flows Missing Company Reporting should get user submitted missing company reports", "status": "passed", "title": "should get user submitted missing company reports", "duration": 0.1680430000000115, "failureMessages": [], "meta": {}}], "startTime": 1756155732270, "endTime": 1756155732275.2227, "status": "passed", "message": "", "name": "/home/<USER>/git/workwell/src/__tests__/user-company-interactions.test.ts"}]}