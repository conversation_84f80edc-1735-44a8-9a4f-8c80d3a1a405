/**
 * Admin Workflow E2E Tests
 * Tests complete admin workflows and management features
 */

import { test, expect, Page } from '@playwright/test'
import { signInAdmin, signInSuperAdmin, signInUser, waitForPageLoad, clearAuth } from './auth-helpers'

test.describe('Admin Workflow E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await clearAuth(page)
    // Add aggressive delay between tests to prevent rate limiting
    await page.waitForTimeout(25000)
  })

  test.skip('Admin Company Management Workflow', async ({ page }) => {
    // Listen for console logs and errors
    page.on('console', msg => {
      if (msg.type() === 'error' || msg.text().includes('AdminPage:')) {
        console.log(`Browser console [${msg.type()}]:`, msg.text())
      }
    })

    page.on('pageerror', error => {
      console.log('Browser page error:', error.message)
    })

    // 1. Sign in as admin
    await signInAdmin(page)
    console.log('After sign in, current URL:', page.url())

    // Check if user is properly authenticated
    const userResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/auth/me')
        return {
          status: response.status,
          data: response.ok ? await response.json() : await response.text()
        }
      } catch (error) {
        return { error: error.message }
      }
    })
    console.log('User authentication check:', userResponse)
    
    // 2. Navigate to admin panel
    console.log('Before admin navigation, current URL:', page.url())
    await page.goto('/admin')
    await waitForPageLoad(page)
    console.log('After admin navigation, current URL:', page.url())
    
    // Should see admin page and wait for it to stabilize
    await expect(page.locator('text=Platform Administration')).toBeVisible({ timeout: 15000 })

    // Wait a bit more for any client-side redirects to complete
    await page.waitForTimeout(3000)
    console.log('After waiting, current URL:', page.url())
    
    // 3. Navigate to company management
    console.log('Before clicking Companies tab, current URL:', page.url())

    // Test the admin companies API directly before clicking the tab
    const companiesApiResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/admin/companies')
        return {
          status: response.status,
          data: response.ok ? await response.json() : await response.text()
        }
      } catch (error) {
        return { error: error.message }
      }
    })
    console.log('Admin companies API response:', companiesApiResponse)

    await page.click('button:has-text("Companies")')
    console.log('After clicking Companies tab, current URL:', page.url())

    // Skip waitForPageLoad and directly wait for companies to appear
    // await waitForPageLoad(page)
    await page.waitForTimeout(2000) // Give it a moment to start loading
    console.log('After short wait, current URL:', page.url())

    // Debug: Check what's actually on the page
    console.log('Page URL:', page.url())
    const pageContent = await page.textContent('body')
    console.log('Page content preview:', pageContent?.substring(0, 500))

    // Check if there are any error messages
    const errorMessages = await page.locator('.bg-red-50, .text-red-600, .error').allTextContents()
    if (errorMessages.length > 0) {
      console.log('Error messages found:', errorMessages)
    }

    // Should see companies list (wait for any company to appear)
    // First check if there are any companies displayed at all
    const companiesSection = page.locator('[data-testid="companies-list"], .companies-list, table')
    await expect(companiesSection.first()).toBeVisible({ timeout: 20000 })

    // Check for specific test companies
    await expect(page.locator('text=E2E Tech Corp').first()).toBeVisible({ timeout: 5000 })
    await expect(page.locator('text=E2E Industries').first()).toBeVisible()
    await expect(page.locator('text=E2E Startup').first()).toBeVisible()
    
    // 4. Create new company
    await page.click('button:has-text("Add Company")')
    await page.fill('input[name="name"]', 'E2E New Company')
    await page.fill('input[name="domain"]', 'newcompany.e2e')
    await page.selectOption('select[name="industry"]', 'Technology')
    await page.selectOption('select[name="size"]', '50-100')
    await page.fill('textarea[name="description"]', 'New company for E2E testing')
    await page.click('button[type="submit"]')
    await waitForPageLoad(page)
    
    // Should see success message and new company
    await expect(page.locator('text=Company created successfully').first()).toBeVisible()
    await expect(page.locator('text=E2E New Company').first()).toBeVisible()
    
    // 5. Edit company
    await page.click('button[aria-label="Edit E2E New Company"]')
    await page.fill('input[name="name"]', 'E2E Updated Company')
    await page.click('button:has-text("Save Changes")')
    await waitForPageLoad(page)
    
    // Should see updated company name
    await expect(page.locator('text=E2E Updated Company')).toBeVisible()
    
    // 6. Verify company
    await page.click('button[aria-label="Verify E2E Updated Company"]')
    await page.click('button:has-text("Confirm Verification")')
    await waitForPageLoad(page)
    
    // Should see verified status
    await expect(page.locator('text=Verified').first()).toBeVisible()
  })

  test.skip('Admin Benefit Management Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to benefit management
    await page.goto('/admin/benefits')
    await waitForPageLoad(page)
    
    // Should see benefits list
    await expect(page.locator('text=E2E Health Insurance')).toBeVisible()
    await expect(page.locator('text=E2E Remote Work')).toBeVisible()
    
    // 3. Create new benefit
    await page.click('button:has-text("Add Benefit")')
    await page.fill('input[name="name"]', 'E2E New Benefit')
    await page.fill('textarea[name="description"]', 'New benefit for E2E testing')
    await page.selectOption('select[name="category"]', 'e2e-cat-1')
    await page.fill('input[name="icon"]', '🎯')
    await page.click('button[type="submit"]')
    await waitForPageLoad(page)
    
    // Should see success message and new benefit
    await expect(page.locator('text=Benefit created successfully')).toBeVisible()
    await expect(page.locator('text=E2E New Benefit')).toBeVisible()
    
    // 4. Edit benefit
    await page.click('button[aria-label="Edit E2E New Benefit"]')
    await page.fill('input[name="name"]', 'E2E Updated Benefit')
    await page.click('button:has-text("Save Changes")')
    await waitForPageLoad(page)
    
    // Should see updated benefit name
    await expect(page.locator('text=E2E Updated Benefit')).toBeVisible()
    
    // 5. Deactivate benefit
    await page.click('button[aria-label="Deactivate E2E Updated Benefit"]')
    await page.click('button:has-text("Confirm Deactivation")')
    await waitForPageLoad(page)
    
    // Should see inactive status
    await expect(page.locator('text=Inactive').first()).toBeVisible()
  })

  test.skip('Admin User Management Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to user management
    await page.goto('/admin/users')
    await waitForPageLoad(page)
    
    // Should see users list
    await expect(page.locator('text=user1@techcorp.e2e')).toBeVisible()
    await expect(page.locator('text=user2@industries.e2e')).toBeVisible()
    
    // 3. Search for specific user
    await page.fill('input[placeholder*="Search users"]', 'user1@techcorp.e2e')
    await waitForPageLoad(page)
    
    // Should see filtered results
    await expect(page.locator('text=user1@techcorp.e2e')).toBeVisible()
    await expect(page.locator('text=user2@industries.e2e')).not.toBeVisible()
    
    // 4. View user details
    await page.click('text=user1@techcorp.e2e')
    await waitForPageLoad(page)
    
    // Should see user profile
    await expect(page.locator('text=John Doe')).toBeVisible()
    await expect(page.locator('text=techcorp.e2e')).toBeVisible()
    
    // 5. Update user information
    await page.click('button:has-text("Edit User")')
    await page.fill('input[name="firstName"]', 'Johnny')
    await page.click('button:has-text("Save Changes")')
    await waitForPageLoad(page)
    
    // Should see updated name
    await expect(page.locator('text=Johnny Doe')).toBeVisible()
    
    // 6. Upgrade user to premium
    await page.click('button:has-text("Upgrade to Premium")')
    await page.click('button:has-text("Confirm Upgrade")')
    await waitForPageLoad(page)
    
    // Should see premium status
    await expect(page.locator('text=Premium User')).toBeVisible()
  })

  test.skip('Admin Benefit Verification Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to benefit verifications
    await page.goto('/admin/verifications')
    await waitForPageLoad(page)
    
    // Should see pending verifications
    await expect(page.locator('text=Pending Verifications')).toBeVisible()
    
    // 3. Review verification request
    const verificationCard = page.locator('.verification-card').first()
    await expect(verificationCard.locator('text=E2E Dental Coverage')).toBeVisible()
    await expect(verificationCard.locator('text=E2E Industries')).toBeVisible()
    
    // 4. Approve verification
    await verificationCard.locator('button:has-text("Approve")').click()
    await page.fill('textarea[name="adminNotes"]', 'Verified through company documentation')
    await page.click('button:has-text("Confirm Approval")')
    await waitForPageLoad(page)
    
    // Should see success message
    await expect(page.locator('text=Verification approved')).toBeVisible()
    
    // 5. Check approved verifications
    await page.click('text=Approved')
    await waitForPageLoad(page)
    
    // Should see the approved verification
    await expect(page.locator('text=E2E Dental Coverage')).toBeVisible()
    await expect(page.locator('text=Approved by Admin User')).toBeVisible()
  })

  test.skip('Admin Analytics and Reporting Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to analytics dashboard
    await page.goto('/admin/analytics')
    await waitForPageLoad(page)
    
    // Should see analytics overview
    await expect(page.locator('text=System Analytics')).toBeVisible()
    await expect(page.locator('text=Total Users')).toBeVisible()
    await expect(page.locator('text=Total Companies')).toBeVisible()
    await expect(page.locator('text=Total Benefits')).toBeVisible()
    
    // 3. View detailed reports
    await page.click('text=User Activity Report')
    await waitForPageLoad(page)
    
    // Should see user activity data
    await expect(page.locator('text=Daily Active Users')).toBeVisible()
    await expect(page.locator('text=Sign-ups This Month')).toBeVisible()
    
    // 4. Export data
    await page.click('button:has-text("Export Data")')
    await page.selectOption('select[name="exportType"]', 'csv')
    await page.selectOption('select[name="dateRange"]', 'last_30_days')
    await page.click('button:has-text("Generate Export")')
    await waitForPageLoad(page)
    
    // Should see export confirmation
    await expect(page.locator('text=Export generated successfully')).toBeVisible()
    
    // 5. View company analytics
    await page.click('text=Company Analytics')
    await waitForPageLoad(page)
    
    // Should see company-specific data
    await expect(page.locator('text=Most Popular Benefits')).toBeVisible()
    await expect(page.locator('text=Company Growth')).toBeVisible()
  })

  test.skip('Super Admin Workflow', async ({ page }) => {
    // 1. Sign in as super admin
    await signInSuperAdmin(page)
    
    // 2. Navigate to super admin panel
    await page.goto('/admin/system')
    await waitForPageLoad(page)
    
    // Should see system management options or admin interface
    const systemManagement = page.locator('text=System Management').first()
    const resetAnalytics = page.locator('text=Reset Analytics').first()
    const manageAdmins = page.locator('text=Manage Admins').first()
    const adminInterface = page.locator('[data-testid="admin-interface"], .admin-interface').first()
    const platformAdmin = page.locator('text=Platform Administration').first()
    const adminDashboard = page.locator('text=Admin Dashboard').first()
    const overviewTab = page.locator('button').filter({ hasText: 'Overview' }).first()

    // At least one admin-related element should be visible
    await expect(systemManagement.or(resetAnalytics).or(manageAdmins).or(adminInterface).or(platformAdmin).or(adminDashboard).or(overviewTab)).toBeVisible({ timeout: 15000 })
    
    // 3. Reset analytics data
    await page.click('button:has-text("Reset Analytics")')
    await page.selectOption('select[name="resetType"]', 'user_analytics')
    await page.fill('input[name="confirmationText"]', 'RESET')
    await page.click('button:has-text("Confirm Reset")')
    await waitForPageLoad(page)
    
    // Should see reset confirmation
    await expect(page.locator('text=Analytics data reset successfully')).toBeVisible()
    
    // 4. Manage admin users
    await page.click('text=Manage Admins')
    await waitForPageLoad(page)
    
    // Should see admin users list
    await expect(page.locator('text=<EMAIL>')).toBeVisible()
    
    // 5. Create new admin
    await page.click('button:has-text("Add Admin")')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="firstName"]', 'New')
    await page.fill('input[name="lastName"]', 'Admin')
    await page.selectOption('select[name="role"]', 'admin')
    await page.click('button[type="submit"]')
    await waitForPageLoad(page)
    
    // Should see new admin in list
    await expect(page.locator('text=<EMAIL>')).toBeVisible()
  })

  test.skip('Admin Error Handling and Security', async ({ page }) => {
    // 1. Try to access admin panel without authentication
    await page.goto('/admin')
    
    // Should redirect to sign-in
    await expect(page).toHaveURL(/\/sign-in/)

    // 2. Sign in as regular user (create fresh token)
    await signInUser(page, 'user1@techcorp.e2e')
    
    // 3. Try to access admin panel as regular user
    await page.goto('/admin')
    
    // Should see access denied or be redirected to sign-in
    const accessDenied = page.locator('text=Access Denied').first()
    const unauthorized = page.locator('text=Unauthorized').first()
    const signInPage = page.locator('text=Sign in to your account').first()
    const errorMessage = page.locator('.error, .bg-red-50').first()
    const notFound = page.locator('text=404').first()
    const forbidden = page.locator('text=403').first()

    // At least one access control element should be visible, or we should be redirected
    const currentUrl = page.url()
    if (currentUrl.includes('/sign-in') || currentUrl.includes('/auth')) {
      console.log('✅ User redirected to sign-in page as expected')
    } else {
      await expect(accessDenied.or(unauthorized).or(signInPage).or(errorMessage).or(notFound).or(forbidden)).toBeVisible({ timeout: 15000 })
    }
    
    // 4. Sign in as admin
    await signInAdmin(page)
    
    // 5. Try to perform unauthorized action
    await page.goto('/admin/system') // Super admin only
    
    // Should see insufficient permissions
    await expect(page.locator('text=Insufficient permissions')).toBeVisible()
    
    // 6. Test form validation
    await page.goto('/admin/companies')
    await page.click('button:has-text("Add Company")')
    await page.click('button[type="submit"]') // Submit without filling required fields
    
    // Should see validation errors
    await expect(page.locator('text=Company name is required')).toBeVisible()
    await expect(page.locator('text=Domain is required')).toBeVisible()
  })

  test.skip('Admin Bulk Operations Workflow', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    
    // 2. Navigate to company management
    await page.goto('/admin/companies')
    await waitForPageLoad(page)
    
    // 3. Select multiple companies
    await page.check('input[type="checkbox"][value="e2e-company-1"]')
    await page.check('input[type="checkbox"][value="e2e-company-2"]')
    
    // Should see bulk actions toolbar
    await expect(page.locator('text=2 companies selected')).toBeVisible()
    
    // 4. Perform bulk verification
    await page.click('button:has-text("Bulk Verify")')
    await page.click('button:has-text("Confirm Bulk Verification")')
    await waitForPageLoad(page)
    
    // Should see success message
    await expect(page.locator('text=Companies verified successfully')).toBeVisible()
    
    // 5. Test bulk export
    await page.check('input[type="checkbox"][value="e2e-company-3"]')
    await page.click('button:has-text("Export Selected")')
    await page.selectOption('select[name="exportFormat"]', 'json')
    await page.click('button:has-text("Generate Export")')
    await waitForPageLoad(page)
    
    // Should see export confirmation
    await expect(page.locator('text=Export generated for 3 companies')).toBeVisible()
  })
})
